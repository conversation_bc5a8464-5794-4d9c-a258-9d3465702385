import { ApiOperation } from '@nestjs/swagger';
import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { UserService } from './user.service';


@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  // login 

  // send-verification-otp

  // verify-otp 

  // forgot-password 

  // verify-forgot-password-otp 

  // reset-password 

  // add-password

  // change-password

  // profile

  // logout
  
}
