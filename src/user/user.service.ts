import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { LoginRegisterDto } from './dto/login-register.dto';
import { User } from './entities/user.entity';

@Injectable()
export class UserService {
  constructor(
    private em: EntityManager
  ){}

  // =============== Main Methods ==================
  async login(loginRegisterDto: LoginRegisterDto){
    const user = await this.em.findOneBy(User, {
      email: LoginRegisterDto.email,
    });
  }

}
